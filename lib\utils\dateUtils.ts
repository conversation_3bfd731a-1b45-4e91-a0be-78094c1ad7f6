/**
 * Date utility functions that provide consistent formatting
 * to prevent hydration mismatches between server and client
 */

/**
 * Format date consistently for display
 * Returns format: DD/MM/YYYY
 */
export function formatDate(date: Date | string): string {
  const dateObj = new Date(date)
  
  if (isNaN(dateObj.getTime())) {
    return 'تاريخ غير صحيح'
  }
  
  const year = dateObj.getFullYear()
  const month = String(dateObj.getMonth() + 1).padStart(2, '0')
  const day = String(dateObj.getDate()).padStart(2, '0')
  
  return `${day}/${month}/${year}`
}

/**
 * Format date and time consistently for display
 * Returns format: DD/MM/YYYY HH:MM
 */
export function formatDateTime(date: Date | string): string {
  const dateObj = new Date(date)
  
  if (isNaN(dateObj.getTime())) {
    return 'تاريخ غير صحيح'
  }
  
  const year = dateObj.getFullYear()
  const month = String(dateObj.getMonth() + 1).padStart(2, '0')
  const day = String(dateObj.getDate()).padStart(2, '0')
  const hours = String(dateObj.getHours()).padStart(2, '0')
  const minutes = String(dateObj.getMinutes()).padStart(2, '0')
  
  return `${day}/${month}/${year} ${hours}:${minutes}`
}

/**
 * Format time only
 * Returns format: HH:MM
 */
export function formatTime(date: Date | string): string {
  const dateObj = new Date(date)
  
  if (isNaN(dateObj.getTime())) {
    return 'وقت غير صحيح'
  }
  
  const hours = String(dateObj.getHours()).padStart(2, '0')
  const minutes = String(dateObj.getMinutes()).padStart(2, '0')
  
  return `${hours}:${minutes}`
}

/**
 * Get relative time in Arabic (e.g., "منذ 5 دقائق")
 * This function is safe for SSR as it doesn't depend on locale
 */
export function getRelativeTime(date: Date | string): string {
  const dateObj = new Date(date)
  const now = new Date()
  const diffMs = now.getTime() - dateObj.getTime()
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffMinutes < 1) {
    return 'الآن'
  } else if (diffMinutes < 60) {
    return `منذ ${diffMinutes} دقيقة`
  } else if (diffHours < 24) {
    return `منذ ${diffHours} ساعة`
  } else if (diffDays < 30) {
    return `منذ ${diffDays} يوم`
  } else {
    return formatDate(dateObj)
  }
}

/**
 * Check if a date is today
 */
export function isToday(date: Date | string): boolean {
  const dateObj = new Date(date)
  const today = new Date()
  
  return dateObj.getDate() === today.getDate() &&
         dateObj.getMonth() === today.getMonth() &&
         dateObj.getFullYear() === today.getFullYear()
}

/**
 * Check if a date is yesterday
 */
export function isYesterday(date: Date | string): boolean {
  const dateObj = new Date(date)
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  
  return dateObj.getDate() === yesterday.getDate() &&
         dateObj.getMonth() === yesterday.getMonth() &&
         dateObj.getFullYear() === yesterday.getFullYear()
}
