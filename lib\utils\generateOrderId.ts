import dayjs from "dayjs"

// ## Generate unique order ID for checkout
export function generateOrderId(): string {
  // Format: RCH-YYYYMMDD-HHMMSS-XXXX
  // RCH = Recharge prefix
  // YYYYMMDD = Date
  // HHMMSS = Time  
  // XXXX = Random 4-digit number
  
  const now = dayjs()
  const dateStr = now.format("YYYYMMDD")
  const timeStr = now.format("HHmmss")
  const randomStr = Math.floor(Math.random() * 9999).toString().padStart(4, "0")
  
  return `RCH-${dateStr}-${timeStr}-${randomStr}`
}

// ## Generate shorter reference ID for display
export function generateShortOrderId(): string {
  // Format: RCH-XXXX-YYYY
  // RCH = Recharge prefix
  // XXXX = Random 4-digit number
  // YYYY = Random 4-digit number
  
  const part1 = Math.floor(Math.random() * 9999).toString().padStart(4, "0")
  const part2 = Math.floor(Math.random() * 9999).toString().padStart(4, "0")
  
  return `RCH-${part1}-${part2}`
}

// ## Generate transaction reference for bank transfers
export function generateTransactionReference(): string {
  // Format: TXN-YYYYMMDD-XXXXXX
  // TXN = Transaction prefix
  // YYYYMMDD = Date
  // XXXXXX = Random 6-digit number
  
  const now = dayjs()
  const dateStr = now.format("YYYYMMDD")
  const randomStr = Math.floor(Math.random() * 999999).toString().padStart(6, "0")
  
  return `TXN-${dateStr}-${randomStr}`
}

// ## Validate order ID format
export function isValidOrderId(orderId: string): boolean {
  // Check if order ID matches the expected format
  const orderIdPattern = /^RCH-\d{8}-\d{6}-\d{4}$/
  const shortOrderIdPattern = /^RCH-\d{4}-\d{4}$/
  
  return orderIdPattern.test(orderId) || shortOrderIdPattern.test(orderId)
}

// ## Extract date from order ID
export function extractDateFromOrderId(orderId: string): Date | null {
  try {
    const parts = orderId.split("-")
    if (parts.length >= 3 && parts[0] === "RCH") {
      const dateStr = parts[1]
      const timeStr = parts[2]
      
      if (dateStr.length === 8 && timeStr.length === 6) {
        const year = parseInt(dateStr.substring(0, 4))
        const month = parseInt(dateStr.substring(4, 6)) - 1 // Month is 0-indexed
        const day = parseInt(dateStr.substring(6, 8))
        const hour = parseInt(timeStr.substring(0, 2))
        const minute = parseInt(timeStr.substring(2, 4))
        const second = parseInt(timeStr.substring(4, 6))
        
        return new Date(year, month, day, hour, minute, second)
      }
    }
    return null
  } catch (error) {
    return null
  }
}
