"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { mockWalletData } from "@/lib/data/mockWalletData"
import { getBalanceForCurrency } from "@/lib/data/mockWalletData"
import { formatCurrency } from "@/lib/data/currencies"
import { Currency } from "@/lib/types"
import { cn } from "@/lib/utils"
import { Wallet, Plus, ShoppingBag } from "lucide-react"

export function HomeWalletCard() {
  const [selectedCurrency, setSelectedCurrency] = useState<Currency>("SDG")
  const router = useRouter()

  // ## Get current balance for selected currency - will be replaced with Supabase query
  const currentBalance = getBalanceForCurrency(mockWalletData, selectedCurrency)

  const handleRecharge = () => {
    router.push("/checkout")
  }

  const handleViewWallet = () => {
    router.push("/wallet")
  }

  const handleShop = () => {
    router.push("/shop")
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">
      {/* Wallet Balance Card */}
      <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl">
                <Wallet className="h-5 w-5 text-slate-900" />
              </div>
              <h3 className="text-white font-bold text-lg">رصيد محفظتك</h3>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleViewWallet}
              className="text-slate-400 hover:text-white"
            >
              عرض الكل
            </Button>
          </div>

          {/* Currency Toggle */}
          <div className="grid grid-cols-2 gap-2 mb-4">
            <button
              onClick={() => setSelectedCurrency("SDG")}
              className={cn(
                "p-3 rounded-xl text-center transition-all duration-300",
                selectedCurrency === "SDG"
                  ? "bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 font-bold"
                  : "bg-slate-700/50 text-slate-300 hover:bg-slate-600/50"
              )}
            >
              <div className="text-sm font-medium">جنيه سوداني</div>
              <div className="text-lg font-bold">
                {formatCurrency(getBalanceForCurrency(mockWalletData, "SDG"), "SDG")}
              </div>
            </button>
            <button
              onClick={() => setSelectedCurrency("EGP")}
              className={cn(
                "p-3 rounded-xl text-center transition-all duration-300",
                selectedCurrency === "EGP"
                  ? "bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 font-bold"
                  : "bg-slate-700/50 text-slate-300 hover:bg-slate-600/50"
              )}
            >
              <div className="text-sm font-medium">جنيه مصري</div>
              <div className="text-lg font-bold">
                {formatCurrency(getBalanceForCurrency(mockWalletData, "EGP"), "EGP")}
              </div>
            </button>
          </div>

          {/* Recharge Button */}
          <Button
            onClick={handleRecharge}
            className="w-full h-12 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold rounded-xl transition-all duration-300 hover:scale-[1.02] shadow-lg hover:shadow-xl"
          >
            <Plus className="h-5 w-5 ml-2" />
            شحن المحفظة
          </Button>
        </CardContent>
      </Card>

      {/* Shop Card */}
      <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-blue-400 to-purple-500 rounded-xl">
                <ShoppingBag className="h-5 w-5 text-white" />
              </div>
              <h3 className="text-white font-bold text-lg">متجر الألعاب</h3>
            </div>
          </div>

          <div className="space-y-3 mb-4">
            <div className="flex justify-between items-center">
              <span className="text-slate-300 text-sm">المنتجات المتاحة</span>
              <span className="text-blue-400 font-bold">50+</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-slate-300 text-sm">الفئات</span>
              <span className="text-purple-400 font-bold">8</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-slate-300 text-sm">العروض النشطة</span>
              <span className="text-green-400 font-bold">12</span>
            </div>
          </div>

          {/* Shop Button */}
          <Button
            onClick={handleShop}
            className="w-full h-12 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-bold rounded-xl transition-all duration-300 hover:scale-[1.02] shadow-lg hover:shadow-xl"
          >
            <ShoppingBag className="h-5 w-5 ml-2" />
            تصفح المتجر
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
