import { Currency, CurrencyInfo } from "@/lib/types"

export const CURRENCIES: Record<Currency, CurrencyInfo> = {
  SDG: {
    code: "SDG",
    name: "Sudanese Pound",
    symbol: "ج.س.",
    arabicName: "الجنيه السوداني"
  },
  EGP: {
    code: "EGP", 
    name: "Egyptian Pound",
    symbol: "ج.م.",
    arabicName: "الجنيه المصري"
  }
}

export const DEFAULT_CURRENCY: Currency = "SDG"

export function formatCurrency(amount: number, currency: Currency): string {
  const currencyInfo = CURRENCIES[currency]
  // Use consistent number formatting without locale dependency
  const formattedAmount = amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  return `${formattedAmount} ${currencyInfo.symbol}`
}

export function getCurrencyInfo(currency: Currency): CurrencyInfo {
  return CURRENCIES[currency]
}
