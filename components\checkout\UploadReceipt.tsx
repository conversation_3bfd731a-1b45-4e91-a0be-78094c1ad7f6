"use client"

import { useState, useRef } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { Upload, X, FileImage, AlertCircle } from "lucide-react"
import Image from "next/image"

interface UploadReceiptProps {
  file: File | null
  preview: string | null
  onFileChange: (file: File | null, preview: string | null) => void
  className?: string
}

export function UploadReceipt({ 
  file, 
  preview, 
  onFileChange, 
  className 
}: UploadReceiptProps) {
  const [dragOver, setDragOver] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const acceptedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"]
  const maxFileSize = 5 * 1024 * 1024 // 5MB

  const validateFile = (file: File): string | null => {
    if (!acceptedTypes.includes(file.type)) {
      return "يرجى اختيار صورة بصيغة JPG أو PNG أو WebP"
    }
    
    if (file.size > maxFileSize) {
      return "حجم الملف يجب أن يكون أقل من 5 ميجابايت"
    }
    
    return null
  }

  const handleFileSelect = (selectedFile: File) => {
    const validationError = validateFile(selectedFile)
    
    if (validationError) {
      setError(validationError)
      return
    }

    setError(null)
    
    // Create preview URL
    const previewUrl = URL.createObjectURL(selectedFile)
    onFileChange(selectedFile, previewUrl)
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      handleFileSelect(selectedFile)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    
    const droppedFile = e.dataTransfer.files[0]
    if (droppedFile) {
      handleFileSelect(droppedFile)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const handleRemoveFile = () => {
    if (preview) {
      URL.revokeObjectURL(preview)
    }
    onFileChange(null, null)
    setError(null)
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Upload Area */}
      {!file && (
        <Card
          className={cn(
            "border-2 border-dashed transition-all duration-300 cursor-pointer",
            "bg-slate-800/30 backdrop-blur-xl",
            dragOver
              ? "border-yellow-400 bg-yellow-400/10"
              : error
              ? "border-red-400 bg-red-400/10"
              : "border-slate-600 hover:border-slate-500 hover:bg-slate-800/50"
          )}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={handleUploadClick}
        >
          <CardContent className="p-8 text-center">
            <div className="flex flex-col items-center gap-4">
              <div
                className={cn(
                  "p-4 rounded-full transition-colors duration-300",
                  dragOver
                    ? "bg-yellow-400/20 text-yellow-400"
                    : error
                    ? "bg-red-400/20 text-red-400"
                    : "bg-slate-700/50 text-slate-400"
                )}
              >
                {error ? (
                  <AlertCircle className="h-8 w-8" />
                ) : (
                  <Upload className="h-8 w-8" />
                )}
              </div>
              
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-white">
                  ارفع صورة الإيصال
                </h3>
                <p className="text-slate-400 text-sm">
                  اسحب الصورة هنا أو انقر للاختيار
                </p>
                <p className="text-slate-500 text-xs">
                  JPG, PNG, WebP (أقل من 5 ميجابايت)
                </p>
              </div>

              <Button
                type="button"
                variant="outline"
                className="border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                <FileImage className="h-4 w-4 ml-2" />
                اختر ملف
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* File Preview */}
      {file && preview && (
        <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
          <CardContent className="p-4">
            <div className="flex items-start gap-4">
              {/* Image Preview */}
              <div className="relative w-24 h-24 rounded-lg overflow-hidden bg-slate-700/50 flex-shrink-0">
                <Image
                  src={preview}
                  alt="Receipt preview"
                  fill
                  className="object-cover"
                />
              </div>

              {/* File Info */}
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-white truncate">
                  {file.name}
                </h4>
                <p className="text-slate-400 text-sm">
                  {(file.size / 1024 / 1024).toFixed(2)} ميجابايت
                </p>
                <p className="text-green-400 text-sm mt-1">
                  تم رفع الملف بنجاح
                </p>
              </div>

              {/* Remove Button */}
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={handleRemoveFile}
                className="text-slate-400 hover:text-red-400 hover:bg-red-400/10"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Message */}
      {error && (
        <div className="p-3 bg-red-400/10 border border-red-400/20 rounded-lg">
          <p className="text-red-400 text-sm text-center">
            {error}
          </p>
        </div>
      )}

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={acceptedTypes.join(",")}
        onChange={handleFileInputChange}
        className="hidden"
      />
    </div>
  )
}
