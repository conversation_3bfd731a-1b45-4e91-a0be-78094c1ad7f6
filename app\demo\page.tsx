"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { GoPlayProductComponent } from "@/components/products/GoPlayProductComponent"
import { AppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { MobileNavigation } from "@/components/layout/MobileNavigation"
import { DesktopFooter } from "@/components/layout/DesktopFooter"
import { 
  Gamepad2, 
  CreditCard, 
  Smartphone, 
  ExternalLink,
  Code,
  Zap
} from "lucide-react"

// ## Demo Products Data
const demoProducts = [
  {
    id: "demo-freefire",
    name: "شحن فري فاير - تجريبي",
    image: "https://c4.wallpaperflare.com/wallpaper/1003/987/595/pubg-player-unknown-battleground-players-hd-wallpaper-preview.jpg",
    description: "مثال على منتج شحن الألعاب مع خيارات متعددة",
    note: "هذا مثال تجريبي - لن يتم تنفيذ الطلب فعلياً",
    basePrice: 0,
    category: "ألعاب الموبايل",
    inStock: true,
    estimatedTime: "أقل من دقيقة",
    features: [
      "شحن فوري",
      "دعم جميع السيرفرات",
      "أسعار تنافسية"
    ],
    fields: [
      {
        label: "أدخل ID فري فاير",
        type: "text" as const,
        name: "freefire_id",
        placeholder: "مثال: 123456789",
        required: true
      }
    ],
    selectors: [
      {
        label: "اختر الباقة",
        name: "package",
        required: true,
        options: [
          { label: "110 جوهرة", price: 2000, value: "110_diamonds" },
          { label: "310 جوهرة", price: 5000, value: "310_diamonds" },
          { label: "520 جوهرة", price: 8000, value: "520_diamonds" }
        ]
      }
    ]
  },
  {
    id: "demo-steam",
    name: "بطاقة Steam - تجريبي",
    image: "https://c4.wallpaperflare.com/wallpaper/1003/987/595/pubg-player-unknown-battleground-players-hd-wallpaper-preview.jpg",
    description: "مثال على بطاقة دفع رقمية",
    note: "هذا مثال تجريبي - لن يتم إرسال بطاقة فعلية",
    basePrice: 50,
    category: "بطاقات الدفع",
    inStock: true,
    estimatedTime: "فوري",
    features: [
      "تفعيل فوري",
      "صالحة عالمياً",
      "دعم فني"
    ],
    fields: [
      {
        label: "البريد الإلكتروني",
        type: "email" as const,
        name: "email",
        placeholder: "<EMAIL>",
        required: true
      },
      {
        label: "ملاحظات",
        type: "textarea" as const,
        name: "notes",
        placeholder: "أي ملاحظات إضافية...",
        required: false
      }
    ],
    selectors: [
      {
        label: "اختر القيمة",
        name: "amount",
        required: true,
        options: [
          { label: "250 ج.س", price: 250, value: "250" },
          { label: "500 ج.س", price: 500, value: "500" },
          { label: "1000 ج.س", price: 1000, value: "1000" }
        ]
      }
    ]
  }
]

export default function DemoPage() {
  const [activeTab, setActiveTab] = useState("home")
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState(demoProducts[0])

  // Demo submission handler
  const handleDemoSubmit = async (data: any) => {
    console.log("Demo Submission:", data)
    alert(`تم إرسال الطلب التجريبي!\n\nالمنتج: ${data.productName}\nالمجموع: ${data.totalPrice} ج.س\n\nهذا مجرد مثال تجريبي.`)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden">
      {/* Background Effects */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl" />
      </div>

      <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />
      <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />

      {/* Main Content */}
      <main className="relative z-10 container mx-auto px-4 py-8 max-w-6xl pt-32">
        {/* Page Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-4">
            صفحة تجريبية
          </h1>
          <p className="text-slate-300 text-lg mb-6">
            مثال على نظام المنتجات الديناميكي - GoPlay Product Component
          </p>
          
          {/* Demo Links */}
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            <Button
              variant="outline"
              onClick={() => window.open('/admin', '_blank')}
              className="border-slate-600 text-slate-300 hover:border-yellow-400 hover:text-yellow-400"
            >
              <Code className="h-4 w-4 mr-2" />
              لوحة التحكم الإدارية
              <ExternalLink className="h-4 w-4 mr-2" />
            </Button>
            
            <Button
              variant="outline"
              onClick={() => window.open('/shop/dynamic/freefire-dynamic', '_blank')}
              className="border-slate-600 text-slate-300 hover:border-blue-400 hover:text-blue-400"
            >
              <Gamepad2 className="h-4 w-4 mr-2" />
              مثال منتج ديناميكي
              <ExternalLink className="h-4 w-4 mr-2" />
            </Button>
          </div>
        </div>

        {/* Product Selector */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-white mb-4 text-center">اختر منتج للتجربة</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto">
            {demoProducts.map((product) => (
              <Card
                key={product.id}
                onClick={() => setSelectedProduct(product)}
                className={`cursor-pointer transition-all duration-300 ${
                  selectedProduct.id === product.id
                    ? "bg-yellow-400/20 border-yellow-400/50"
                    : "bg-slate-800/50 border-slate-700/50 hover:bg-slate-700/50"
                }`}
              >
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 rounded-lg overflow-hidden bg-slate-700/50 flex items-center justify-center">
                      {product.category === "ألعاب الموبايل" ? (
                        <Gamepad2 className="h-6 w-6 text-blue-400" />
                      ) : product.category === "بطاقات الدفع" ? (
                        <CreditCard className="h-6 w-6 text-green-400" />
                      ) : (
                        <Smartphone className="h-6 w-6 text-purple-400" />
                      )}
                    </div>
                    <div>
                      <h3 className="text-white font-medium">{product.name}</h3>
                      <p className="text-slate-400 text-sm">{product.category}</p>
                    </div>
                    {selectedProduct.id === product.id && (
                      <Badge className="bg-yellow-400/20 text-yellow-400 border-yellow-400/30 mr-auto">
                        محدد
                      </Badge>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Demo Component */}
        <div className="mb-8">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-white mb-2">
              مكون المنتج الديناميكي
            </h2>
            <p className="text-slate-300">
              يتكيف تلقائياً مع بيانات المنتج ويحسب الأسعار ديناميكياً
            </p>
          </div>
          
          <GoPlayProductComponent
            product={selectedProduct}
            onSubmit={handleDemoSubmit}
            className="max-w-4xl mx-auto"
          />
        </div>

        {/* Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Zap className="h-5 w-5 text-yellow-400" />
                ديناميكي ومرن
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-slate-300 text-sm">
                يتكيف مع أي نوع من المنتجات - حقول مخصصة، قوائم اختيار، حساب أسعار تلقائي
              </p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Smartphone className="h-5 w-5 text-blue-400" />
                متجاوب تماماً
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-slate-300 text-sm">
                يعمل بشكل مثالي على جميع الأجهزة - موبايل، تابلت، ديسكتوب
              </p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Code className="h-5 w-5 text-green-400" />
                جاهز للإنتاج
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-slate-300 text-sm">
                مُعد للتكامل مع Supabase - تعليقات واضحة لنقاط التكامل
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Technical Info */}
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-white">المعلومات التقنية</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="text-white font-medium mb-2">المكونات المُنشأة:</h4>
                <ul className="text-slate-300 space-y-1">
                  <li>• GoPlayProductComponent - مكون المنتج الديناميكي</li>
                  <li>• CategoryDashboard - إدارة الفئات</li>
                  <li>• ProductDashboard - إدارة المنتجات</li>
                  <li>• صفحات تجريبية ومثالية</li>
                </ul>
              </div>
              <div>
                <h4 className="text-white font-medium mb-2">المميزات:</h4>
                <ul className="text-slate-300 space-y-1">
                  <li>• حقول ديناميكية (نص، إيميل، منطقة نص)</li>
                  <li>• قوائم اختيار مع أسعار مختلفة</li>
                  <li>• حساب السعر الإجمالي تلقائياً</li>
                  <li>• تحقق من صحة البيانات</li>
                  <li>• تصميم متجاوب ومتوافق مع الموقع</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>

      <MobileNavigation activeTab={activeTab} onTabChange={() => {}} />
      <DesktopFooter activeTab={activeTab} onTabChange={() => {}} />
    </div>
  )
}
