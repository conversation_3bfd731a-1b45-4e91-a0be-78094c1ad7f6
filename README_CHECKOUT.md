# Arabic Wallet Recharge Checkout System

## Overview
A comprehensive frontend-only wallet recharge checkout system built with Next.js, TypeScript, and Tailwind CSS. Features Arabic RTL UI/UX, 3-step checkout flow, admin configuration panel, and preparation for future Supabase backend integration.

## Features
- ✅ **3-Step Checkout Flow**: التفاصيل → ملخص الطلب → الدفع
- ✅ **Arabic RTL Layout**: Full right-to-left support with proper Arabic typography
- ✅ **Galaxy Design System**: Consistent styling with slate-800/50 backdrop-blur cards and yellow-to-orange gradients
- ✅ **Mobile Responsive**: Optimized for all device sizes with mobile-first design
- ✅ **Admin Configuration**: Manage bank accounts and preset amounts
- ✅ **File Upload**: Receipt upload with drag-and-drop support
- ✅ **Currency Support**: SDG (Sudanese Pound) and EGP (Egyptian Pound)
- ✅ **Order Management**: Generated order IDs with localStorage persistence
- ✅ **Home Page Integration**: Wallet balance card with recharge functionality

## Architecture

### Core Components
- `components/pages/CheckoutPage.tsx` - Main checkout page with stepper navigation
- `components/checkout/CheckoutContext.tsx` - React context for state management
- `components/checkout/Step1Details.tsx` - Amount selection step
- `components/checkout/Step2OrderSummary.tsx` - User details and order summary
- `components/checkout/Step3Payment.tsx` - Bank selection and payment
- `components/pages/CheckoutSuccessPage.tsx` - Order confirmation page
- `components/pages/CheckoutConfigPage.tsx` - Admin configuration panel
- `components/home/<USER>

### Reusable Components
- `components/checkout/ProgressStepper.tsx` - Mobile/desktop stepper navigation
- `components/checkout/BankOptionCard.tsx` - Bank selection cards
- `components/checkout/UploadReceipt.tsx` - File upload with preview
- `components/checkout/AmountSelector.tsx` - Preset/custom amount selection

### Data & Types
- `lib/types/index.ts` - TypeScript interfaces for checkout system
- `lib/utils/localStorage.ts` - Configuration and order management
- `lib/utils/generateOrderId.ts` - Order ID generation utilities

## Routes
- `/checkout` - Main checkout page
- `/checkout/success?orderId=xxx` - Success confirmation
- `/admin/checkout-config` - Admin configuration panel

## State Management
The checkout system uses React Context API with reducer pattern:

```typescript
interface CheckoutData {
  step: 1 | 2 | 3
  amount: number
  currency: Currency
  userDetails: CheckoutUserDetails | null
  selectedBank: BankAccount | null
  referenceNumber: string
  receiptFile: File | null
  receiptPreview: string | null
}
```

## Configuration
Admin can configure:
- **Bank Accounts**: Name, account number, logo
- **Preset Amounts**: Quick selection buttons (10k, 25k, 50k, 100k)
- **Checkout Notes**: Instructions for users

Configuration is stored in localStorage and ready for Supabase migration.

## Backend Integration Preparation

### Supabase Tables (Ready for Implementation)

#### 1. checkout_config
```sql
CREATE TABLE checkout_config (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  bank_accounts JSONB NOT NULL DEFAULT '[]',
  recharge_options JSONB NOT NULL DEFAULT '[]',
  checkout_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 2. checkout_orders
```sql
CREATE TABLE checkout_orders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  order_id TEXT UNIQUE NOT NULL,
  user_id UUID REFERENCES auth.users(id),
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT NOT NULL CHECK (currency IN ('SDG', 'EGP')),
  user_details JSONB NOT NULL,
  selected_bank JSONB NOT NULL,
  reference_number TEXT NOT NULL,
  receipt_url TEXT,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 3. wallet_transactions
```sql
CREATE TABLE wallet_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  order_id TEXT REFERENCES checkout_orders(order_id),
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT NOT NULL,
  type TEXT DEFAULT 'recharge',
  status TEXT DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### API Endpoints (Ready for Implementation)

#### 1. Configuration Management
- `GET /api/checkout/config` - Get checkout configuration
- `PUT /api/checkout/config` - Update configuration (admin only)

#### 2. Order Management
- `POST /api/checkout/orders` - Create new order
- `GET /api/checkout/orders/:orderId` - Get order details
- `PUT /api/checkout/orders/:orderId/status` - Update order status (admin)

#### 3. File Upload
- `POST /api/checkout/upload-receipt` - Upload receipt file to storage

### Frontend Integration Points
All components include `## TODO:` comments marking Supabase integration points:

```typescript
// ## TODO: Replace with Supabase query
// const { data: config } = await supabase
//   .from('checkout_config')
//   .select('*')
//   .single()

// ## TODO: Replace with Supabase insert
// const { data: order } = await supabase
//   .from('checkout_orders')
//   .insert(orderData)
//   .select()
//   .single()
```

## Testing Checklist

### Functional Testing
- [ ] Step 1: Amount selection (preset + custom)
- [ ] Step 2: User details form validation
- [ ] Step 3: Bank selection and receipt upload
- [ ] Order submission and success page
- [ ] Admin configuration panel
- [ ] Home page wallet integration

### Mobile Testing
- [ ] Responsive design on all screen sizes
- [ ] Touch-friendly buttons and inputs
- [ ] Proper RTL Arabic layout
- [ ] File upload on mobile devices

### Browser Testing
- [ ] Chrome/Edge (Chromium)
- [ ] Firefox
- [ ] Safari (if available)
- [ ] Mobile browsers

## Development Commands

```bash
# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build

# Type checking
npm run type-check

# Linting
npm run lint
```

## File Structure
```
components/
├── checkout/
│   ├── CheckoutContext.tsx
│   ├── ProgressStepper.tsx
│   ├── Step1Details.tsx
│   ├── Step2OrderSummary.tsx
│   ├── Step3Payment.tsx
│   ├── BankOptionCard.tsx
│   ├── UploadReceipt.tsx
│   └── AmountSelector.tsx
├── pages/
│   ├── CheckoutPage.tsx
│   ├── CheckoutSuccessPage.tsx
│   └── CheckoutConfigPage.tsx
└── home/
    └── HomeWalletCard.tsx

app/
├── checkout/
│   ├── page.tsx
│   └── success/
│       └── page.tsx
└── admin/
    └── checkout-config/
        └── page.tsx

lib/
├── types/index.ts
└── utils/
    ├── localStorage.ts
    └── generateOrderId.ts
```

## Next Steps
1. **Backend Integration**: Implement Supabase tables and API endpoints
2. **File Storage**: Set up Supabase Storage for receipt uploads
3. **Authentication**: Add user authentication and admin roles
4. **Real-time Updates**: Add order status notifications
5. **Payment Gateway**: Integrate with actual payment providers
6. **Analytics**: Add order tracking and reporting

## Support
For questions or issues, refer to the component documentation and Supabase integration comments throughout the codebase.
