"use client"

import { X, ChevronRight } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { menuItems } from "@/lib/data/menuItems"
import { useRouter } from "next/navigation"

interface SideMenuProps {
  isOpen: boolean
  onClose: () => void
}

export function SideMenu({ isOpen, onClose }: SideMenuProps) {
  const router = useRouter()

  // Handle navigation
  const handleNavigation = (href: string) => {
    onClose() // Close menu first

    if (href.startsWith("#")) {
      // Handle anchor links (scroll to section or placeholder)
      if (href === "#offers" || href === "#contact" || href === "#about") {
        // ## TODO: Implement scroll to section or create dedicated pages
        console.log(`Navigate to section: ${href}`)
      }
    } else {
      // Handle route navigation
      router.push(href)
    }
  }

  return (
    <>
      {/* Side Menu Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 transition-opacity duration-300"
          onClick={onClose}
        />
      )}

      {/* Side Menu */}
      <div
        className={`fixed top-0 right-0 h-full w-80 lg:w-96 bg-white/10 backdrop-blur-2xl z-50 transform transition-transform duration-300 ease-out border-l border-white/20 shadow-2xl ${
          isOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div className="p-6 pt-24 lg:pt-28">
          {/* Menu Header */}
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
              رايه شوب
            </h2>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="text-white hover:bg-white/10 rounded-full"
            >
              <X className="h-6 w-6" />
            </Button>
          </div>

          {/* Menu Items */}
          <nav className="space-y-2">
            {menuItems.map((item, index) => (
              <button
                key={index}
                onClick={() => handleNavigation(item.href)}
                className="w-full flex items-center justify-between p-4 rounded-xl hover:bg-white/20 backdrop-blur-sm transition-all duration-300 group border border-transparent hover:border-white/30 text-right"
              >
                <div className="flex items-center gap-4">
                  <div className="text-yellow-400 group-hover:scale-110 transition-transform duration-300">
                    {item.icon}
                  </div>
                  <span className="font-medium text-lg text-white">{item.label}</span>
                </div>
                <ChevronRight className="h-4 w-4 text-slate-400 group-hover:text-white transition-colors duration-300" />
              </button>
            ))}
          </nav>

          {/* Menu Footer */}
          <div className="absolute bottom-6 left-6 right-6">
            <div className="bg-gradient-to-r from-yellow-400/30 to-orange-500/30 backdrop-blur-sm rounded-xl p-4 border border-yellow-400/40 shadow-lg">
              <p className="text-sm text-center text-yellow-400 font-medium">🎮 أفضل متجر للألعاب الرقمية</p>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
